import { useState } from 'react';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import DButton from '../Global/DButton';
import DInput from '../Global/DInput/DInput';
import DModal from '../Global/DModal';
import * as chatService from '@/services/chat.service';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

const DModalEmailTransaction = ({ open, onClose, token, setNotification, notification }) => {
    const currectConversation = useConversationStore(state => state.currentConversation);
    const [email, setEmail] = useState('');
    const timezone = useTimezoneStore((state) => state.timezone);


    const handleGetChatTranscript = async () => {
        if(currectConversation.id === null) {
            setNotification({
                show: true,
                type: 'negative',
                message: 'No conversation id found',
            });
            return;
        }
        try{
            const response = await chatService.sendEmailTranscript(currectConversation.id, token, email, timezone);
            if(response.status === 201){
                setNotification({
                    show: true,
                    type: 'positive',
                    message: 'Email sent successfully',
                });
                onClose();
            }
        } catch (error) {
            console.log(error);
        }
    }

    return (
        <DModal
            isOpen={open}
            onClose={onClose}
            title="Chat transcript"
            footer={
                <DButton
                variant="dark"
                fullWidth
                onClick={handleGetChatTranscript}
                >
                Get chat transcript
                </DButton>
            }
        >
            <div className="flex flex-col gap-size1">
                <p className="text-sm font-regular tracking-tight text-grey-50"> This email is strictly for communication purposes and will not be stored, shared, or used
                for any marketing activities.</p>
                <DInput
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Email address"
                />
                {notification?.show && (
                    <div className={`text-sm font-regular tracking-tight ${
                        notification.type === 'positive' ? 'text-green-800' : 'text-red-800'
                    }`}>
                        {notification.message}
                    </div>
                )}
            </div>
        </DModal>
    );
};

export default DModalEmailTransaction;
